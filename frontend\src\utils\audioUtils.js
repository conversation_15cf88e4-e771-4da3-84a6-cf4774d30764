/**
 * 音频工具类
 * 
 * 提供语音录制、播放、格式转换等功能
 * 为STT、TTS、Whisper等功能预留接口
 */

class AudioUtils {
  constructor() {
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;
    this.audioContext = null;
    this.stream = null;
    
    // 音频配置
    this.config = {
      sampleRate: 16000,
      channels: 1,
      format: 'wav',
      maxDuration: 300, // 最大录制时长（秒）
      minDuration: 1    // 最小录制时长（秒）
    };
  }

  /**
   * 初始化音频上下文
   */
  async initAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      return true;
    } catch (error) {
      console.error('音频上下文初始化失败:', error);
      return false;
    }
  }

  /**
   * 请求麦克风权限
   */
  async requestMicrophonePermission() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      this.stream = stream;
      return true;
    } catch (error) {
      console.error('麦克风权限请求失败:', error);
      throw new Error('无法访问麦克风，请检查权限设置');
    }
  }

  /**
   * 开始录音
   */
  async startRecording() {
    try {
      if (this.isRecording) {
        throw new Error('正在录音中');
      }

      // 请求麦克风权限
      await this.requestMicrophonePermission();

      // 创建MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      this.audioChunks = [];
      this.isRecording = true;

      // 监听数据事件
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      // 监听停止事件
      this.mediaRecorder.onstop = () => {
        this.isRecording = false;
      };

      // 开始录音
      this.mediaRecorder.start(100); // 每100ms收集一次数据

      console.log('开始录音');
      return true;
    } catch (error) {
      console.error('开始录音失败:', error);
      this.isRecording = false;
      throw error;
    }
  }

  /**
   * 停止录音
   */
  async stopRecording() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording || !this.mediaRecorder) {
        reject(new Error('当前没有在录音'));
        return;
      }

      this.mediaRecorder.onstop = async () => {
        try {
          // 创建音频Blob
          const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
          
          // 转换为WAV格式
          const wavBlob = await this.convertToWav(audioBlob);
          
          // 转换为base64
          const base64Audio = await this.blobToBase64(wavBlob);
          
          // 清理资源
          this.cleanup();
          
          console.log('录音完成');
          resolve({
            blob: wavBlob,
            base64: base64Audio,
            duration: this.calculateDuration(wavBlob)
          });
        } catch (error) {
          console.error('停止录音失败:', error);
          reject(error);
        }
      };

      this.mediaRecorder.stop();
    });
  }

  /**
   * 取消录音
   */
  cancelRecording() {
    if (this.isRecording && this.mediaRecorder) {
      this.mediaRecorder.stop();
      this.cleanup();
      console.log('录音已取消');
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;
  }

  /**
   * 将Blob转换为base64
   */
  blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * 转换为WAV格式
   * 注意：这是一个简化的实现，实际项目中可能需要更复杂的音频处理
   */
  async convertToWav(webmBlob) {
    try {
      // 这里应该实现WebM到WAV的转换
      // 为了简化，暂时直接返回原始blob
      // 实际部署时需要使用专业的音频转换库
      console.warn('音频格式转换功能需要实现');
      return webmBlob;
    } catch (error) {
      console.error('音频格式转换失败:', error);
      return webmBlob;
    }
  }

  /**
   * 计算音频时长
   */
  calculateDuration(audioBlob) {
    // 这里应该实现音频时长计算
    // 为了简化，返回估算值
    return Math.round(audioBlob.size / 16000); // 粗略估算
  }

  /**
   * 播放音频
   */
  async playAudio(audioUrl) {
    try {
      const audio = new Audio(audioUrl);
      
      return new Promise((resolve, reject) => {
        audio.onended = resolve;
        audio.onerror = reject;
        audio.play();
      });
    } catch (error) {
      console.error('音频播放失败:', error);
      throw error;
    }
  }

  /**
   * 检查浏览器支持
   */
  static checkBrowserSupport() {
    const support = {
      mediaRecorder: !!window.MediaRecorder,
      getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      audioContext: !!(window.AudioContext || window.webkitAudioContext),
      webAudio: !!window.AudioContext
    };

    const isSupported = Object.values(support).every(Boolean);
    
    return {
      isSupported,
      details: support
    };
  }

  /**
   * 获取音频设备列表
   */
  static async getAudioDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return {
        microphones: devices.filter(device => device.kind === 'audioinput'),
        speakers: devices.filter(device => device.kind === 'audiooutput')
      };
    } catch (error) {
      console.error('获取音频设备失败:', error);
      return { microphones: [], speakers: [] };
    }
  }
}

/**
 * 语音服务API接口
 * 
 * 与后端语音服务进行通信的接口类
 */
class VoiceService {
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
  }

  /**
   * 语音转文字 (STT)
   * 
   * @param {string} audioData - base64编码的音频数据
   * @param {string} sessionId - 会话ID
   * @param {string} language - 语言代码，默认'zh-CN'
   * @param {string} format - 音频格式，默认'wav'
   */
  async speechToText(audioData, sessionId, language = 'zh-CN', format = 'wav') {
    try {
      const response = await fetch(`${this.baseURL}/conversation/audio/stt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          audio_data: audioData,
          session_id: sessionId,
          language: language,
          format: format
        })
      });

      if (!response.ok) {
        throw new Error(`STT请求失败: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('语音转文字失败:', error);
      throw error;
    }
  }

  /**
   * 文字转语音 (TTS)
   * 
   * @param {string} text - 要转换的文本
   * @param {string} sessionId - 会话ID
   * @param {string} voiceId - 语音ID
   * @param {number} speed - 语速，默认1.0
   * @param {number} pitch - 音调，默认1.0
   */
  async textToSpeech(text, sessionId, voiceId = 'default', speed = 1.0, pitch = 1.0) {
    try {
      const response = await fetch(`${this.baseURL}/conversation/audio/tts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: text,
          session_id: sessionId,
          voice_id: voiceId,
          speed: speed,
          pitch: pitch,
          language: 'zh-CN'
        })
      });

      if (!response.ok) {
        throw new Error(`TTS请求失败: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('文字转语音失败:', error);
      throw error;
    }
  }

  /**
   * Whisper语音识别
   * 
   * @param {string} audioData - base64编码的音频数据
   * @param {string} sessionId - 会话ID
   * @param {string} language - 语言代码，'auto'为自动检测
   */
  async whisperTranscribe(audioData, sessionId, language = 'auto') {
    try {
      // 这里调用Whisper API接口
      // 实际部署时需要配置Whisper服务
      console.log('调用Whisper API - 接口预留');
      
      // 模拟API调用
      return {
        text: '这是Whisper识别的结果（模拟）',
        confidence: 0.95,
        language: 'zh',
        duration: 3.5
      };
    } catch (error) {
      console.error('Whisper识别失败:', error);
      throw error;
    }
  }
}

// 创建全局实例
const audioUtils = new AudioUtils();
const voiceService = new VoiceService();

export { AudioUtils, VoiceService, audioUtils, voiceService };
