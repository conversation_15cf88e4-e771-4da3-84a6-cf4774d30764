<template>
  <div class="virtual-patient-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <h1 class="app-title">
          <i class="icon-medical"></i>
          虚拟患者对话系统
        </h1>
        <div class="header-actions">
          <button class="btn btn-secondary" @click="showHistory">
            <i class="icon-history"></i>
            历史记录
          </button>
          <button class="btn btn-primary" @click="showSettings">
            <i class="icon-settings"></i>
            设置
          </button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 开始界面 -->
      <div v-if="!sessionStarted" class="start-section">
        <div class="start-container">
          <div class="welcome-content">
            <h2 class="welcome-title">欢迎使用虚拟患者对话系统</h2>
            <p class="welcome-description">
              与AI虚拟患者进行医患对话练习，提升您的诊疗技能和沟通能力
            </p>

            <!-- 配置选项 -->
            <div class="config-options">
              <div class="config-group">
                <label class="config-label">难度等级:</label>
                <select v-model="sessionConfig.difficulty" class="config-select">
                  <option value="1">初级</option>
                  <option value="2">中级</option>
                  <option value="3">高级</option>
                  <option value="4">专家</option>
                </select>
              </div>

              <div class="config-group">
                <label class="config-label">科室偏好:</label>
                <select v-model="sessionConfig.department" class="config-select">
                  <option value="">随机</option>
                  <option value="internal_medicine">内科</option>
                  <option value="surgery">外科</option>
                  <option value="pediatrics">儿科</option>
                  <option value="cardiology">心内科</option>
                  <option value="neurology">神经科</option>
                </select>
              </div>

              <div class="config-group">
                <label class="config-label">患者年龄:</label>
                <select v-model="sessionConfig.ageGroup" class="config-select">
                  <option value="">随机</option>
                  <option value="child">儿童</option>
                  <option value="young_adult">青年</option>
                  <option value="middle_aged">中年</option>
                  <option value="elderly">老年</option>
                </select>
              </div>
            </div>

            <!-- 开始按钮 -->
            <button
              class="btn btn-large btn-primary start-btn"
              :disabled="isGeneratingPatient"
              :class="{ 'loading': isGeneratingPatient }"
              @click="startSession"
            >
              <i class="icon-play" v-if="!isGeneratingPatient"></i>
              <i class="icon-loading" v-else></i>
              {{ isGeneratingPatient ? '正在生成患者...' : '开始对话' }}
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
export default {
  name: 'VirtualPatientHome',
  data() {
    return {
      sessionStarted: false,
      isGeneratingPatient: false,
      sessionConfig: {
        difficulty: 2,
        department: '',
        ageGroup: ''
      }
    };
  },
  methods: {
    async startSession() {
      this.isGeneratingPatient = true;
      try {
        // 调用后端API生成虚拟患者
        const response = await this.$http.post('/api/virtual-patient/generate', this.sessionConfig);

        if (response.data.success) {
          // 保存患者信息和会话ID
          this.$store.commit('setCurrentPatient', response.data.patient);
          this.$store.commit('setSessionId', response.data.session_id);

          // 跳转到对话界面
          this.$router.push('/conversation');
        } else {
          this.$message.error('生成虚拟患者失败，请重试');
        }
      } catch (error) {
        console.error('生成患者失败:', error);
        this.$message.error('系统错误，请稍后重试');
      } finally {
        this.isGeneratingPatient = false;
      }
    },

    showHistory() {
      this.$router.push('/history');
    },

    showSettings() {
      this.$router.push('/settings');
    }
  }
};
</script>

<style scoped>
.virtual-patient-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

/* 顶部导航栏 */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 20px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  height: 70px;
}

.app-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-title .icon-medical {
  font-size: 28px;
  color: #3498db;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 开始界面 */
.start-section {
  width: 100%;
  max-width: 800px;
}

.start-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 60px 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-title {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-description {
  font-size: 18px;
  color: #7f8c8d;
  margin-bottom: 40px;
  line-height: 1.6;
}

/* 配置选项 */
.config-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  text-align: left;
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-weight: 600;
  color: #34495e;
  font-size: 14px;
}

.config-select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
  outline: none;
}

.config-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #ecf0f1;
  color: #34495e;
  border: 1px solid #bdc3c7;
}

.btn-secondary:hover:not(:disabled) {
  background: #d5dbdb;
  transform: translateY(-1px);
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
  border-radius: 12px;
}

.start-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
  position: relative;
  overflow: hidden;
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.5);
}

.start-btn.loading {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 图标样式 */
.icon-medical::before { content: '🏥'; }
.icon-history::before { content: '📋'; }
.icon-settings::before { content: '⚙️'; }
.icon-play::before { content: '▶️'; }
.icon-loading::before {
  content: '⏳';
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .app-title {
    font-size: 20px;
  }

  .start-container {
    padding: 40px 20px;
    margin: 0 10px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .config-options {
    grid-template-columns: 1fr;
  }
}
</style>