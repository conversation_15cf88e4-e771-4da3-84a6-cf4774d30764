"""
后端打包脚本
使用 PyInstaller 将 FastAPI 应用打包为可执行文件
"""

import os
import sys
import subprocess
import shutil

def build_backend():
    """打包后端应用"""
    print("开始打包后端应用...")
    
    # 安装 PyInstaller
    print("安装 PyInstaller...")
    subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 创建打包命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包为单个文件
        "--name", "virtual-patient-system",  # 可执行文件名
        "--distpath", "dist",  # 输出目录
        "--workpath", "build",  # 工作目录
        "--specpath", ".",  # spec文件目录
        "--add-data", "templates;templates",  # 包含模板文件
        "--hidden-import", "uvicorn",
        "--hidden-import", "fastapi",
        "--hidden-import", "jinja2",
        "--hidden-import", "motor",
        "--hidden-import", "pymongo",
        "app/main.py"
    ]
    
    print("执行打包命令...")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 后端打包成功！")
        print(f"可执行文件位置: {os.path.abspath('dist/virtual-patient-system.exe')}")
        
        # 创建启动脚本
        create_startup_script()
    else:
        print("❌ 打包失败:")
        print(result.stderr)

def create_startup_script():
    """创建启动脚本"""
    startup_script = """@echo off
echo 正在启动虚拟患者对话系统...
echo.
echo 系统将在 http://127.0.0.1:8000 启动
echo 请在浏览器中访问上述地址
echo.
echo 按 Ctrl+C 停止服务
echo.

virtual-patient-system.exe

pause
"""
    
    with open("dist/start.bat", "w", encoding="utf-8") as f:
        f.write(startup_script)
    
    print("✅ 启动脚本已创建: dist/start.bat")

if __name__ == "__main__":
    build_backend()
