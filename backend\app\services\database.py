import os
import motor.motor_asyncio
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class DatabaseService:
    """数据库服务类"""
    
    def __init__(self):
        self.client: Optional[motor.motor_asyncio.AsyncIOMotorClient] = None
        self.database = None
        
    async def connect(self):
        """连接数据库"""
        try:
            # 从环境变量获取MongoDB连接字符串，如果没有则使用默认值
            mongodb_url = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
            database_name = os.getenv("DATABASE_NAME", "virtual_patient_db")
            
            # 创建异步MongoDB客户端
            self.client = motor.motor_asyncio.AsyncIOMotorClient(mongodb_url)
            self.database = self.client[database_name]
            
            # 测试连接
            await self.client.admin.command('ping')
            logger.info(f"成功连接到MongoDB数据库: {database_name}")
            
            # 创建索引
            await self._create_indexes()
            
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    async def disconnect(self):
        """断开数据库连接"""
        if self.client:
            self.client.close()
            logger.info("数据库连接已关闭")
    
    async def _create_indexes(self):
        """创建数据库索引"""
        try:
            # 虚拟患者集合索引
            await self.database.virtual_patients.create_index("patient_id", unique=True)
            await self.database.virtual_patients.create_index("difficulty_level")
            await self.database.virtual_patients.create_index("correct_department")
            await self.database.virtual_patients.create_index("age_group")
            await self.database.virtual_patients.create_index("gender")
            
            # 对话会话集合索引
            await self.database.conversation_sessions.create_index("session_id", unique=True)
            await self.database.conversation_sessions.create_index("patient_id")
            await self.database.conversation_sessions.create_index("start_time")
            await self.database.conversation_sessions.create_index("status")
            
            # 对话消息集合索引
            await self.database.conversation_messages.create_index("session_id")
            await self.database.conversation_messages.create_index("timestamp")
            await self.database.conversation_messages.create_index("round_number")
            
            # 分析结果集合索引
            await self.database.conversation_analyses.create_index("session_id", unique=True)
            await self.database.conversation_analyses.create_index("analysis_time")
            
            logger.info("数据库索引创建完成")
            
        except Exception as e:
            logger.error(f"创建数据库索引失败: {str(e)}")
    
    def get_collection(self, collection_name: str):
        """获取集合"""
        if not self.database:
            raise Exception("数据库未连接")
        return self.database[collection_name]

# 全局数据库实例
db_service = DatabaseService()

async def get_database():
    """获取数据库实例"""
    if not db_service.database:
        await db_service.connect()
    return db_service

async def init_database():
    """初始化数据库"""
    await db_service.connect()

async def close_database():
    """关闭数据库连接"""
    await db_service.disconnect()
