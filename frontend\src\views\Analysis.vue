<template>
  <div class="analysis-container">
    <!-- 左侧用户信息区域 -->
    <div class="user-info-panel">
      <div class="user-icon">
        <el-avatar :size="100" icon="el-icon-user"></el-avatar>
      </div>
      <div class="user-text">
        <h3>未登录用户</h3>
      </div>
      
      <!-- 对话记录区域 -->
      <div class="chat-history-panel">
        <h4>聊天记录(计算对话)</h4>
        <div class="chat-history-content" ref="chatHistory">
          <div v-for="(msg, index) in chatHistory" :key="index" 
               :class="['chat-message', msg.type === 'user' ? 'user-message' : 'system-message']">
            <div class="message-content">{{ msg.content }}</div>
            <div class="message-time">{{ msg.time }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧主要内容区域 -->
    <div class="main-content">
      <!-- 顶部导航 -->
      <div class="top-nav">
        <el-button @click="goBack" icon="el-icon-arrow-left" plain>返回</el-button>
      </div>
      
      <!-- 中间分析区域 -->
      <div class="analysis-area">
        <div class="prompt-area">
          <h2>请描述您当前的情绪和感受</h2>
          <p>您可以详细描述您的感受、想法或最近发生的事情，我们将进行分析并提供建议。</p>
          
          <el-form :model="analysisForm" ref="analysisForm">
            <el-form-item prop="content">
              <el-input 
                v-model="analysisForm.content" 
                type="textarea" 
                :rows="8"
                placeholder="请在此输入您的描述..."
              ></el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="submitAnalysis" 
                :loading="loading"
                class="submit-btn"
              >
                提交分析
              </el-button>
              <el-button @click="resetForm" class="reset-btn">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AnalysisPage',
  data() {
    return {
      // 分析表单数据
      analysisForm: {
        content: ''
      },
      // 加载状态
      loading: false,
      // 聊天历史
      chatHistory: []
    };
  },
  methods: {
    // 返回首页
    goBack() {
      this.$router.push('/');
    },
    
    // 提交分析
    async submitAnalysis() {
      if (!this.analysisForm.content.trim()) {
        this.$message.warning('请输入您的描述内容');
        return;
      }
      
      this.loading = true;
      
      // 添加用户消息到聊天记录
      this.addChatMessage('user', this.analysisForm.content);
      
      try {
        // 调用后端API进行分析
        // 注意：这里是模拟API调用，实际项目中应替换为真实API
        // const response = await this.$axios.post('/api/analyze', {
        //   content: this.analysisForm.content
        // });
        
        // 模拟API响应延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 模拟系统回复
        this.addChatMessage('system', '您的情绪分析已完成，正在生成报告...');
        
        // 显示分析完成动画
        this.$message({
          message: '分析完成！正在生成报告...',
          type: 'success',
          duration: 2000,
          onClose: () => {
            // 动画完成后再跳转到结果页面
            this.loading = false;
            // 跳转到结果页面，并传递分析内容
            this.$router.push({
              path: '/result',
              query: { id: new Date().getTime() } // 使用时间戳作为临时ID
            });
          }
        });
      } catch (error) {
        console.error('分析请求失败:', error);
        this.$message.error('分析请求失败，请稍后重试');
        this.loading = false;
      }
    },
    
    // 重置表单
    resetForm() {
      this.analysisForm.content = '';
    },
    
    // 添加聊天消息
    addChatMessage(type, content) {
      const now = new Date();
      const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      
      this.chatHistory.push({
        type,
        content,
        time: timeStr
      });
      
      // 滚动到底部
      this.$nextTick(() => {
        if (this.$refs.chatHistory) {
          this.$refs.chatHistory.scrollTop = this.$refs.chatHistory.scrollHeight;
        }
      });
    }
  }
};
</script>

<style scoped>
.analysis-container {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
}

.user-info-panel {
  width: 300px;
  background-color: white;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.user-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.user-text {
  text-align: center;
  margin-bottom: 20px;
}

.chat-history-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e6e6e6;
  padding-top: 15px;
}

.chat-history-content {
  flex: 1;
  overflow-y: auto;
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.chat-message {
  margin-bottom: 12px;
  max-width: 85%;
  padding: 8px 12px;
  border-radius: 8px;
  position: relative;
}

.user-message {
  align-self: flex-end;
  background-color: #e1f3ff;
  margin-left: auto;
}

.system-message {
  align-self: flex-start;
  background-color: #f0f0f0;
}

.message-content {
  word-break: break-word;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: right;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.top-nav {
  margin-bottom: 20px;
}

.analysis-area {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
  overflow-y: auto;
}

.prompt-area {
  max-width: 800px;
  margin: 0 auto;
}

.prompt-area h2 {
  margin-bottom: 16px;
  color: #333;
}

.prompt-area p {
  margin-bottom: 30px;
  color: #666;
  line-height: 1.6;
}

.submit-btn, .reset-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.submit-btn:hover, .reset-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.submit-btn:active, .reset-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-btn::before, .reset-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.submit-btn:hover::before, .reset-btn:hover::before {
  left: 100%;
}

/* 添加表单元素的过渡效果 */
.el-form-item {
  transition: all 0.3s ease;
}

.el-input__inner, .el-textarea__inner {
  transition: border 0.3s ease, box-shadow 0.3s ease;
}

.el-input__inner:focus, .el-textarea__inner:focus {
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
}
</style>