import { createRouter, createWebHashHistory } from 'vue-router';

// 导入页面组件
const Home = () => import('./views/Home.vue');
const Conversation = () => import('./views/Conversation.vue');
const Result = () => import('./views/Result.vue');

// 创建路由实例
const router = createRouter({
  // 使用hash模式，适合Electron应用
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home,
      meta: { title: '虚拟患者对话系统' }
    },
    {
      path: '/conversation',
      name: 'Conversation',
      component: Conversation,
      meta: {
        title: '对话练习',
        requiresSession: true
      }
    },
    {
      path: '/result',
      name: 'Result',
      component: Result,
      meta: {
        title: '分析结果',
        requiresAnalysis: true
      }
    },
    // 默认重定向到首页
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
});

// 路由前置守卫
router.beforeEach((to, from, next) => {
  // 设置窗口标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }

  // 检查会话要求
  if (to.meta.requiresSession) {
    const sessionId = localStorage.getItem('sessionId');
    if (!sessionId) {
      console.warn('需要有效会话，重定向到首页');
      next('/');
      return;
    }
  }

  // 检查分析结果要求
  if (to.meta.requiresAnalysis) {
    const analysisResult = localStorage.getItem('analysisResult');
    if (!analysisResult) {
      console.warn('需要分析结果，重定向到首页');
      next('/');
      return;
    }
  }

  next();
});

export default router;