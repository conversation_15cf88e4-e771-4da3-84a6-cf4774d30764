import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import axios from 'axios';

// 配置axios默认URL
axios.defaults.baseURL = process.env.NODE_ENV === 'production'
  ? 'http://localhost:8000'
  : 'http://localhost:8000';

// 创建Vue应用实例
const app = createApp(App);

// 全局配置
app.config.globalProperties.$axios = axios;

// 使用插件
app.use(router);
app.use(ElementPlus);

// 挂载应用
app.mount('#app');