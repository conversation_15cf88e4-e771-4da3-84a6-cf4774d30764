# Core FastAPI dependencies
fastapi
uvicorn
pydantic

# HTTP client
httpx

# Database
motor
pymongo

# System monitoring
psutil

# Basic utilities
python-multipart
python-dotenv
requests
jinja2

# File processing
aiofiles

# Date utilities
python-dateutil

# Logging (usually built-in, but just in case)
# logging is built-in

# JSON processing (built-in json module should work)
# orjson  # Optional, can use built-in json

# Email validation (if needed)
# email-validator

# PDF generation (optional for now)
# reportlab
# weasyprint

# CORS (FastAPI has built-in CORS middleware)
# fastapi-cors
