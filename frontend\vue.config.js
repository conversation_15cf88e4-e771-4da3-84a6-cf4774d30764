module.exports = {
  pluginOptions: {
    electronBuilder: {
      nodeIntegration: true,
      builderOptions: {
        // 配置打包选项
        appId: 'com.emotion-analysis-assistant.app',
        productName: '情绪分析助手',
        copyright: 'Copyright © 2023',
        // 支持Windows和Linux
        win: {
          icon: './public/icon.png',
          target: [
            {
              target: 'nsis',
              arch: ['x64']
            }
          ]
        },
        linux: {
          icon: './public/icon.png',
          target: [
            {
              target: 'AppImage',
              arch: ['x64']
            },
            {
              target: 'deb',
              arch: ['x64']
            }
          ],
          category: 'Utility'
        },
        // 配置自动更新
        publish: ['github'],
        // 打包后的文件名格式
        artifactName: '${productName}-${version}-${os}-${arch}.${ext}'
      },
      // 主进程文件
      mainProcessFile: 'src/background.js',
      // 渲染进程文件
      rendererProcessFile: 'src/main.js'
    }
  },
  // 开发服务器配置
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  },

  // Webpack 配置
  configureWebpack: {
    resolve: {
      fallback: {
        "events": require.resolve("events/")
      }
    }
  }
};