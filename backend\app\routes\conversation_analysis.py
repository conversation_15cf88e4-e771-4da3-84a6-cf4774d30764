"""
对话分析路由

提供虚拟患者对话的分析评估接口
"""

from fastapi import APIRouter, HTTPException, Path, Query
from typing import Dict, Any, Optional
import logging

from ..services.analysis_service import analysis_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/conversation-analysis", tags=["对话分析"])

@router.post("/trigger/{session_id}")
async def trigger_analysis(session_id: str = Path(..., description="会话ID")):
    """触发对话分析（使用原有分析方法）"""
    try:
        analysis = await analysis_service.analyze_conversation(session_id)
        return {
            "success": True,
            "message": "分析完成",
            "analysis": analysis.dict()
        }
    except Exception as e:
        logger.error(f"触发分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.post("/advanced/{session_id}")
async def trigger_advanced_analysis(session_id: str = Path(..., description="会话ID")):
    """触发高级对话分析（使用新分析引擎）"""
    try:
        analysis_result = await analysis_service.analyze_conversation_advanced(session_id)
        return {
            "success": True,
            "message": "高级分析完成",
            "analysis_result": analysis_result
        }
    except Exception as e:
        logger.error(f"高级分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"高级分析失败: {str(e)}")

@router.get("/result/{session_id}")
async def get_analysis_result(session_id: str = Path(..., description="会话ID")):
    """获取分析结果"""
    try:
        # 从数据库获取分析结果
        db = await analysis_service._get_db()
        
        # 首先尝试获取高级分析结果
        advanced_result = await db.advanced_analysis_results.find_one(
            {"session_id": session_id},
            sort=[("analysis_timestamp", -1)]
        )
        
        if advanced_result:
            # 移除MongoDB的_id字段
            advanced_result.pop("_id", None)
            return {
                "success": True,
                "analysis_type": "advanced",
                "analysis_result": advanced_result
            }
        
        # 如果没有高级分析结果，尝试获取基础分析结果
        basic_result = await db.conversation_analyses.find_one(
            {"session_id": session_id},
            sort=[("analysis_timestamp", -1)]
        )
        
        if basic_result:
            basic_result.pop("_id", None)
            return {
                "success": True,
                "analysis_type": "basic",
                "analysis_result": basic_result
            }
        
        raise HTTPException(status_code=404, detail="未找到分析结果")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分析结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析结果失败: {str(e)}")

@router.get("/history")
async def get_analysis_history(
    limit: int = Query(10, description="返回结果数量限制"),
    offset: int = Query(0, description="偏移量")
):
    """获取分析历史记录"""
    try:
        db = await analysis_service._get_db()
        
        # 获取高级分析结果
        advanced_results = await db.advanced_analysis_results.find(
            {},
            {"session_id": 1, "overall_score": 1, "grade": 1, "analysis_timestamp": 1}
        ).sort("analysis_timestamp", -1).skip(offset).limit(limit).to_list(length=limit)
        
        # 获取基础分析结果
        basic_results = await db.conversation_analyses.find(
            {},
            {"session_id": 1, "overall_score": 1, "analysis_timestamp": 1}
        ).sort("analysis_timestamp", -1).skip(offset).limit(limit).to_list(length=limit)
        
        # 合并结果
        all_results = []
        
        for result in advanced_results:
            result.pop("_id", None)
            result["analysis_type"] = "advanced"
            all_results.append(result)
        
        for result in basic_results:
            result.pop("_id", None)
            result["analysis_type"] = "basic"
            all_results.append(result)
        
        # 按时间排序
        all_results.sort(key=lambda x: x["analysis_timestamp"], reverse=True)
        
        return {
            "success": True,
            "history": all_results[:limit],
            "total": len(all_results)
        }
        
    except Exception as e:
        logger.error(f"获取分析历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析历史失败: {str(e)}")

@router.delete("/result/{session_id}")
async def delete_analysis_result(session_id: str = Path(..., description="会话ID")):
    """删除分析结果"""
    try:
        db = await analysis_service._get_db()
        
        # 删除高级分析结果
        advanced_delete_result = await db.advanced_analysis_results.delete_many(
            {"session_id": session_id}
        )
        
        # 删除基础分析结果
        basic_delete_result = await db.conversation_analyses.delete_many(
            {"session_id": session_id}
        )
        
        total_deleted = advanced_delete_result.deleted_count + basic_delete_result.deleted_count
        
        if total_deleted == 0:
            raise HTTPException(status_code=404, detail="未找到要删除的分析结果")
        
        return {
            "success": True,
            "message": f"成功删除 {total_deleted} 条分析结果",
            "deleted_count": total_deleted
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分析结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除分析结果失败: {str(e)}")

@router.get("/statistics")
async def get_analysis_statistics():
    """获取分析统计信息"""
    try:
        db = await analysis_service._get_db()
        
        # 统计高级分析结果
        advanced_count = await db.advanced_analysis_results.count_documents({})
        
        # 统计基础分析结果
        basic_count = await db.conversation_analyses.count_documents({})
        
        # 获取平均分数
        advanced_avg_pipeline = [
            {"$group": {"_id": None, "avg_score": {"$avg": "$overall_score"}}}
        ]
        advanced_avg_result = await db.advanced_analysis_results.aggregate(advanced_avg_pipeline).to_list(length=1)
        advanced_avg_score = advanced_avg_result[0]["avg_score"] if advanced_avg_result else 0
        
        basic_avg_pipeline = [
            {"$group": {"_id": None, "avg_score": {"$avg": "$overall_score"}}}
        ]
        basic_avg_result = await db.conversation_analyses.aggregate(basic_avg_pipeline).to_list(length=1)
        basic_avg_score = basic_avg_result[0]["avg_score"] if basic_avg_result else 0
        
        # 获取评级分布（仅高级分析）
        grade_pipeline = [
            {"$group": {"_id": "$grade", "count": {"$sum": 1}}}
        ]
        grade_distribution = await db.advanced_analysis_results.aggregate(grade_pipeline).to_list(length=None)
        
        return {
            "success": True,
            "statistics": {
                "total_analyses": advanced_count + basic_count,
                "advanced_analyses": advanced_count,
                "basic_analyses": basic_count,
                "average_scores": {
                    "advanced": round(advanced_avg_score, 2) if advanced_avg_score else 0,
                    "basic": round(basic_avg_score, 2) if basic_avg_score else 0
                },
                "grade_distribution": {item["_id"]: item["count"] for item in grade_distribution}
            }
        }
        
    except Exception as e:
        logger.error(f"获取分析统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析统计失败: {str(e)}")

@router.post("/batch-analyze")
async def batch_analyze_conversations(session_ids: list[str]):
    """批量分析多个对话"""
    try:
        results = []
        failed_sessions = []
        
        for session_id in session_ids:
            try:
                analysis_result = await analysis_service.analyze_conversation_advanced(session_id)
                results.append({
                    "session_id": session_id,
                    "success": True,
                    "overall_score": analysis_result["overall_score"],
                    "grade": analysis_result["grade"]
                })
            except Exception as e:
                failed_sessions.append({
                    "session_id": session_id,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"批量分析完成，成功: {len(results)}, 失败: {len(failed_sessions)}",
            "results": results,
            "failed_sessions": failed_sessions
        }
        
    except Exception as e:
        logger.error(f"批量分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量分析失败: {str(e)}")

@router.get("/export/{session_id}")
async def export_analysis_result(
    session_id: str = Path(..., description="会话ID"),
    format: str = Query("json", description="导出格式: json, csv, pdf")
):
    """导出分析结果"""
    try:
        # 获取分析结果
        db = await analysis_service._get_db()
        result = await db.advanced_analysis_results.find_one({"session_id": session_id})
        
        if not result:
            raise HTTPException(status_code=404, detail="未找到分析结果")
        
        result.pop("_id", None)
        
        if format.lower() == "json":
            return {
                "success": True,
                "format": "json",
                "data": result
            }
        elif format.lower() == "csv":
            # TODO: 实现CSV导出
            return {
                "success": True,
                "format": "csv",
                "message": "CSV导出功能开发中"
            }
        elif format.lower() == "pdf":
            # TODO: 实现PDF导出
            return {
                "success": True,
                "format": "pdf",
                "message": "PDF导出功能开发中"
            }
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出分析结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出分析结果失败: {str(e)}")
