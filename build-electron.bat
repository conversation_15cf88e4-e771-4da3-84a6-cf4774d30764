@echo off
echo 正在构建虚拟患者对话系统...

echo.
echo 1. 构建后端可执行文件...
cd backend
python build.py
if errorlevel 1 (
    echo 后端构建失败
    pause
    exit /b 1
)

echo.
echo 2. 复制后端文件到 Electron 资源目录...
if not exist "..\frontend\dist_electron" mkdir "..\frontend\dist_electron"
if not exist "..\frontend\dist_electron\backend" mkdir "..\frontend\dist_electron\backend"
copy "dist\virtual-patient-system.exe" "..\frontend\dist_electron\backend\"
xcopy "templates" "..\frontend\dist_electron\backend\templates\" /E /I /Y

cd ..

echo.
echo 3. 构建 Electron 应用...
cd frontend
call npm run electron:build
if errorlevel 1 (
    echo Electron 构建失败
    pause
    exit /b 1
)

echo.
echo ✅ 构建完成！
echo 可执行文件位置: frontend\dist_electron\
pause
