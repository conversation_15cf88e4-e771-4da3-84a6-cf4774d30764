'use strict';

import { app, protocol, BrowserWindow, ipcMain, dialog } from 'electron';
import { createProtocol } from 'vue-cli-plugin-electron-builder/lib';
import installExtension, { VUEJS3_DEVTOOLS } from 'electron-devtools-installer';
import path from 'path';
import { spawn } from 'child_process';
import fs from 'fs';

const isDevelopment = process.env.NODE_ENV !== 'production';

// 保存后端进程引用
let backendProcess = null;

// 启动后端服务
function startBackendServer() {
  // 判断是开发环境还是生产环境
  const isPackaged = app.isPackaged;
  let scriptPath;
  
  if (isPackaged) {
    // 生产环境，使用打包后的后端路径
    scriptPath = path.join(process.resourcesPath, 'backend');
  } else {
    // 开发环境，使用相对路径
    scriptPath = path.join(__dirname, '../../backend');
  }

  // 检查后端目录是否存在
  if (!fs.existsSync(scriptPath)) {
    console.error('后端目录不存在:', scriptPath);
    return;
  }

  // 根据操作系统选择不同的启动命令
  let command;
  let args;
  
  if (process.platform === 'win32') {
    // Windows
    command = 'python';
    args = [
      '-m',
      'uvicorn',
      'app.main:app',
      '--host',
      '127.0.0.1',
      '--port',
      '8000',
      '--reload'
    ];
  } else {
    // Linux/macOS
    command = 'python';
    args = [
      '-m',
      'uvicorn',
      'app.main:app',
      '--host',
      '127.0.0.1',
      '--port',
      '8000',
      '--reload'
    ];
  }

  console.log('启动后端服务:', command, args);
  
  // 启动后端进程
  backendProcess = spawn(command, args, {
    shell: true,
    windowsHide: true,
    cwd: scriptPath // 设置工作目录为后端目录
  });

  // 监听后端输出
  backendProcess.stdout.on('data', (data) => {
    console.log(`后端输出: ${data}`);
  });

  backendProcess.stderr.on('data', (data) => {
    console.error(`后端错误: ${data}`);
  });

  backendProcess.on('close', (code) => {
    console.log(`后端进程退出，退出码 ${code}`);
    backendProcess = null;
  });
}

// 关闭后端服务
function stopBackendServer() {
  if (backendProcess) {
    if (process.platform === 'win32') {
      // Windows 上使用 taskkill 强制终止进程树
      spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
    } else {
      // Linux/macOS
      backendProcess.kill('SIGTERM');
    }
    backendProcess = null;
  }
}

// 全局保存主窗口引用，避免被JavaScript垃圾回收
let win;

// 注册自定义协议
protocol.registerSchemesAsPrivileged([
  { scheme: 'app', privileges: { secure: true, standard: true } }
]);

async function createWindow() {
  // 创建浏览器窗口
  win = new BrowserWindow({
    width: 1024,
    height: 768,
    webPreferences: {
      // 启用Node集成，允许在渲染进程中使用Node.js API
      nodeIntegration: true,
      contextIsolation: false,
      // 在Electron 12+中，需要显式设置为false以允许require
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../public/icon.png')
  });

  // 设置窗口标题
  win.setTitle('情绪分析助手');

  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // 开发环境：加载开发服务器URL
    await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL);
    if (!process.env.IS_TEST) win.webContents.openDevTools();
  } else {
    createProtocol('app');
    // 生产环境：加载index.html
    win.loadURL('app://./index.html');
  }

  // 窗口关闭事件处理
  win.on('closed', () => {
    win = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.on('ready', async () => {
  if (isDevelopment && !process.env.IS_TEST) {
    // 安装Vue Devtools
    try {
      await installExtension(VUEJS3_DEVTOOLS);
    } catch (e) {
      console.error('Vue Devtools 安装失败:', e.toString());
    }
  }
  
  // 启动后端服务
  startBackendServer();
  
  // 创建主窗口
  createWindow();
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  // 在macOS上，应用和菜单栏通常会保持活跃状态，直到用户使用Cmd + Q明确退出
  if (process.platform !== 'darwin') {
    stopBackendServer();
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标且没有其他窗口打开时，通常会在应用程序中重新创建一个窗口
  if (win === null) {
    createWindow();
  }
});

// 应用退出前清理
app.on('before-quit', () => {
  stopBackendServer();
});

// 处理PDF导出
ipcMain.handle('export-pdf', async (event, data) => {
  const { filePath } = await dialog.showSaveDialog({
    title: '导出PDF报告',
    defaultPath: '情绪分析报告.pdf',
    filters: [{ name: 'PDF文件', extensions: ['pdf'] }]
  });
  
  if (!filePath) return { success: false, message: '导出已取消' };
  
  // 这里只是返回路径，实际PDF生成在渲染进程中处理
  return { success: true, filePath };
});

// 退出开发模式下的webpack热更新
if (isDevelopment) {
  if (process.platform === 'win32') {
    process.on('message', (data) => {
      if (data === 'graceful-exit') {
        stopBackendServer();
        app.quit();
      }
    });
  } else {
    process.on('SIGTERM', () => {
      stopBackendServer();
      app.quit();
    });
  }
}