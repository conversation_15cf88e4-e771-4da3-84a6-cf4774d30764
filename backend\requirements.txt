# FastAPI框架及相关依赖
fastapi>=0.100.0
uvicorn>=0.23.0
pydantic>=2.0.0
email-validator>=2.0.0

# 系统监控
psutil>=5.9.0

# 数据处理
python-multipart>=0.0.6
python-dotenv>=1.0.0

# MongoDB数据库驱动
pymongo>=4.5.0
motor>=3.3.0

# 工具库
python-dateutil>=2.8.2
requests>=2.31.0

# PDF生成
reportlab>=4.0.0
weasyprint>=60.0

# 模板引擎
jinja2>=3.1.0

# 文件处理
aiofiles>=23.0.0

# JSON处理
orjson>=3.9.0

# 跨域支持
fastapi-cors>=0.0.6

# 注意：以下模型相关依赖需要根据实际情况添加
# 自然语言处理（如果需要）
# nltk>=3.6.5,<3.7.0
# spacy>=3.1.3,<3.2.0
# transformers>=4.11.3,<4.12.0

# 机器学习（如果需要）
# scikit-learn>=1.0.0,<1.1.0
# tensorflow>=2.6.0,<2.7.0
# torch>=1.9.0,<1.10.0